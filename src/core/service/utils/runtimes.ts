import { Layer, ManagedRuntime } from "effect";
import { areaApiRepoLive } from "src/modules/area/service/repo/api/area-api";
import { AreaRepositoryLive } from "src/modules/area/service/repository";
import { areaUsecaseLive } from "src/modules/area/service/usecase";
import { authApiRepoLive } from "src/modules/auth/service/repo/api/auth-api";
import { authRepositoryLive } from "src/modules/auth/service/repository";
import { authUsecaseLive } from "src/modules/auth/service/usecase";
import { brandApiRepoLive } from "src/modules/brand/service/repo/api/brand-api";
import { BrandRepositoryLive } from "src/modules/brand/service/repository";
import { brandUsecaseLive } from "src/modules/brand/service/usecase";
import { businessLineApiRepoLive } from "src/modules/business-line/service/repo/api/business-line-api";
import { BusinessLineRepositoryLive } from "src/modules/business-line/service/repository";
import { businessLineUsecaseLive } from "src/modules/business-line/service/usecase";
import { categoryApiRepoLive } from "src/modules/category/service/repo/api/category-api";
import { CategoryRepositoryLive } from "src/modules/category/service/repository";
import { categoryUsecaseLive } from "src/modules/category/service/usecase";
import { measurementUnitApiRepoLive } from "src/modules/measurement-unit/service/repo/api/measurement-unit-api";
import { unitMeasurementCategoryApiRepoLive } from "src/modules/measurement-unit/service/repo/api/unit-measurement-category-api";
import { MeasurementUnitRepositoryLive } from "src/modules/measurement-unit/service/repository";
import { UnitMeasurementCategoryRepositoryLive } from "src/modules/measurement-unit/service/unit-measurement-category-repository";
import { unitMeasurementCategoryUsecaseLive } from "src/modules/measurement-unit/service/unit-measurement-category-usecase";
import { measurementUnitUsecaseLive } from "src/modules/measurement-unit/service/usecase";
import { operationApiRepoLive } from "src/modules/operation/service/repo/api/operation-api";
import { OperationRepositoryLive } from "src/modules/operation/service/repository";
import { operationUsecaseLive } from "src/modules/operation/service/usecase";
import { productApiRepoLive } from "src/modules/product/service/repo/api/product-api";
import { ProductRepositoryLive } from "src/modules/product/service/repository";
import { productUsecaseLive } from "src/modules/product/service/usecase";
import { productionFlowApiRepoLive } from "src/modules/production-flow/service/repo/api/production-flow-api";
import { ProductionFlowRepositoryLive } from "src/modules/production-flow/service/repository";
import { productionFlowUsecaseLive } from "src/modules/production-flow/service/usecase";
import { recipeApiRepoLive } from "src/modules/recipe/service/repo/api/recipe-api";
import { RecipeRepositoryLive } from "src/modules/recipe/service/repository";
import { recipeUsecaseLive } from "src/modules/recipe/service/usecase";
import { workAreaApiRepoLive } from "src/modules/work-area/service/repo/api/work-area-api";
import { WorkAreaRepositoryLive } from "src/modules/work-area/service/repository";
import { workAreaUsecaseLive } from "src/modules/work-area/service/usecase";
import { channelApiRepoLive } from "~/modules/channel/service/repo/api/channel-api";
import { ChannelRepositoryLive } from "~/modules/channel/service/repository";
import { channelUsecaseLive } from "~/modules/channel/service/usecase";
import { warehouseApiRepoLive } from "~/modules/warehouse/service/repo/api/warehouse-api";
import { WarehouseRepositoryLive } from "~/modules/warehouse/service/repository";
import { warehouseUsecaseLive } from "~/modules/warehouse/service/usecase";

const makeAreaUsecaseLive = areaUsecaseLive.pipe(
	Layer.provide(AreaRepositoryLive),
	Layer.provide(areaApiRepoLive),
);

const makeAuthUsecaseLive = authUsecaseLive.pipe(
	Layer.provide(authRepositoryLive),
	Layer.provide(authApiRepoLive),
);

const makeBrandUsecaseLive = brandUsecaseLive.pipe(
	Layer.provide(BrandRepositoryLive),
	Layer.provide(brandApiRepoLive),
);

const makeBusinessLineUsecaseLive = businessLineUsecaseLive.pipe(
	Layer.provide(BusinessLineRepositoryLive),
	Layer.provide(businessLineApiRepoLive),
);

const makeCategoryUsecaseLive = categoryUsecaseLive.pipe(
	Layer.provide(CategoryRepositoryLive),
	Layer.provide(categoryApiRepoLive),
);

const makeMeasurementUnitUsecaseLive = measurementUnitUsecaseLive.pipe(
	Layer.provide(MeasurementUnitRepositoryLive),
	Layer.provide(measurementUnitApiRepoLive),
);

const makeUnitMeasurementCategoryUsecaseLive =
	unitMeasurementCategoryUsecaseLive.pipe(
		Layer.provide(UnitMeasurementCategoryRepositoryLive),
		Layer.provide(unitMeasurementCategoryApiRepoLive),
	);

const makeOperationUsecaseLive = operationUsecaseLive.pipe(
	Layer.provide(OperationRepositoryLive),
	Layer.provide(operationApiRepoLive),
);

const makeProductUsecaseLive = productUsecaseLive.pipe(
	Layer.provide(ProductRepositoryLive),
	Layer.provide(productApiRepoLive),
);

const makeWorkAreaUsecaseLive = workAreaUsecaseLive.pipe(
	Layer.provide(WorkAreaRepositoryLive),
	Layer.provide(workAreaApiRepoLive),
);

const makeProductionFlowUsecaseLive = productionFlowUsecaseLive.pipe(
	Layer.provide(ProductionFlowRepositoryLive),
	Layer.provide(productionFlowApiRepoLive),
);

const makeRecipeUsecaseLive = recipeUsecaseLive.pipe(
	Layer.provide(RecipeRepositoryLive),
	Layer.provide(recipeApiRepoLive),
);

const makeWarehouseUsecaseLive = warehouseUsecaseLive.pipe(
	Layer.provide(WarehouseRepositoryLive),
	Layer.provide(warehouseApiRepoLive),
);

const makeChannelUsecaseLive = channelUsecaseLive.pipe(
	Layer.provide(ChannelRepositoryLive),
	Layer.provide(channelApiRepoLive),
);

const MainLayer = Layer.mergeAll(
	makeAreaUsecaseLive,
	makeAuthUsecaseLive,
	makeBrandUsecaseLive,
	makeBusinessLineUsecaseLive,
	makeCategoryUsecaseLive,
	makeMeasurementUnitUsecaseLive,
	makeUnitMeasurementCategoryUsecaseLive,
	makeOperationUsecaseLive,
	makeProductUsecaseLive,
	makeRecipeUsecaseLive,
	makeWorkAreaUsecaseLive,
	makeProductionFlowUsecaseLive,
	makeWarehouseUsecaseLive,
	makeChannelUsecaseLive,
);

export const AppRuntime = ManagedRuntime.make(MainLayer);
