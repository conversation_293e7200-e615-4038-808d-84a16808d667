import { Layer, ManagedRuntime } from "effect";
import { areaApiRepoLive } from "~/area/service/repo/api/area-api";
import { AreaRepositoryLive } from "~/area/service/repository";
import { areaUsecaseLive } from "~/area/service/usecase";
import { authApiRepoLive } from "~/auth/service/repo/api/auth-api";
import { authRepositoryLive } from "~/auth/service/repository";
import { authUsecaseLive } from "~/auth/service/usecase";
import { brandApiRepoLive } from "~/brand/service/repo/api/brand-api";
import { BrandRepositoryLive } from "~/brand/service/repository";
import { brandUsecaseLive } from "~/brand/service/usecase";
import { businessLineApiRepoLive } from "~/business-line/service/repo/api/business-line-api";
import { BusinessLineRepositoryLive } from "~/business-line/service/repository";
import { businessLineUsecaseLive } from "~/business-line/service/usecase";
import { categoryApiRepoLive } from "~/category/service/repo/api/category-api";
import { CategoryRepositoryLive } from "~/category/service/repository";
import { categoryUsecaseLive } from "~/category/service/usecase";
import { channelApiRepoLive } from "~/channel/service/repo/api/channel-api";
import { ChannelRepositoryLive } from "~/channel/service/repository";
import { channelUsecaseLive } from "~/channel/service/usecase";
import { measurementUnitApiRepoLive } from "~/measurement-unit/service/repo/api/measurement-unit-api";
import { unitMeasurementCategoryApiRepoLive } from "~/measurement-unit/service/repo/api/unit-measurement-category-api";
import { MeasurementUnitRepositoryLive } from "~/measurement-unit/service/repository";
import { UnitMeasurementCategoryRepositoryLive } from "~/measurement-unit/service/unit-measurement-category-repository";
import { unitMeasurementCategoryUsecaseLive } from "~/measurement-unit/service/unit-measurement-category-usecase";
import { measurementUnitUsecaseLive } from "~/measurement-unit/service/usecase";
import { operationApiRepoLive } from "~/operation/service/repo/api/operation-api";
import { OperationRepositoryLive } from "~/operation/service/repository";
import { operationUsecaseLive } from "~/operation/service/usecase";
import { productApiRepoLive } from "~/product/service/repo/api/product-api";
import { ProductRepositoryLive } from "~/product/service/repository";
import { productUsecaseLive } from "~/product/service/usecase";
import { productionFlowApiRepoLive } from "~/production-flow/service/repo/api/production-flow-api";
import { ProductionFlowRepositoryLive } from "~/production-flow/service/repository";
import { productionFlowUsecaseLive } from "~/production-flow/service/usecase";
import { recipeApiRepoLive } from "~/recipe/service/repo/api/recipe-api";
import { RecipeRepositoryLive } from "~/recipe/service/repository";
import { recipeUsecaseLive } from "~/recipe/service/usecase";
import { sellerApiRepoLive } from "~/seller/service/repo/api/seller-api";
import { SellerRepositoryLive } from "~/seller/service/repository";
import { sellerUsecaseLive } from "~/seller/service/usecase";
import { warehouseApiRepoLive } from "~/warehouse/service/repo/api/warehouse-api";
import { WarehouseRepositoryLive } from "~/warehouse/service/repository";
import { warehouseUsecaseLive } from "~/warehouse/service/usecase";
import { workAreaApiRepoLive } from "~/work-area/service/repo/api/work-area-api";
import { WorkAreaRepositoryLive } from "~/work-area/service/repository";
import { workAreaUsecaseLive } from "~/work-area/service/usecase";

const makeAreaUsecaseLive = areaUsecaseLive.pipe(
	Layer.provide(AreaRepositoryLive),
	Layer.provide(areaApiRepoLive),
);

const makeAuthUsecaseLive = authUsecaseLive.pipe(
	Layer.provide(authRepositoryLive),
	Layer.provide(authApiRepoLive),
);

const makeBrandUsecaseLive = brandUsecaseLive.pipe(
	Layer.provide(BrandRepositoryLive),
	Layer.provide(brandApiRepoLive),
);

const makeBusinessLineUsecaseLive = businessLineUsecaseLive.pipe(
	Layer.provide(BusinessLineRepositoryLive),
	Layer.provide(businessLineApiRepoLive),
);

const makeCategoryUsecaseLive = categoryUsecaseLive.pipe(
	Layer.provide(CategoryRepositoryLive),
	Layer.provide(categoryApiRepoLive),
);

const makeMeasurementUnitUsecaseLive = measurementUnitUsecaseLive.pipe(
	Layer.provide(MeasurementUnitRepositoryLive),
	Layer.provide(measurementUnitApiRepoLive),
);

const makeUnitMeasurementCategoryUsecaseLive =
	unitMeasurementCategoryUsecaseLive.pipe(
		Layer.provide(UnitMeasurementCategoryRepositoryLive),
		Layer.provide(unitMeasurementCategoryApiRepoLive),
	);

const makeOperationUsecaseLive = operationUsecaseLive.pipe(
	Layer.provide(OperationRepositoryLive),
	Layer.provide(operationApiRepoLive),
);

const makeProductUsecaseLive = productUsecaseLive.pipe(
	Layer.provide(ProductRepositoryLive),
	Layer.provide(productApiRepoLive),
);

const makeWorkAreaUsecaseLive = workAreaUsecaseLive.pipe(
	Layer.provide(WorkAreaRepositoryLive),
	Layer.provide(workAreaApiRepoLive),
);

const makeProductionFlowUsecaseLive = productionFlowUsecaseLive.pipe(
	Layer.provide(ProductionFlowRepositoryLive),
	Layer.provide(productionFlowApiRepoLive),
);

const makeRecipeUsecaseLive = recipeUsecaseLive.pipe(
	Layer.provide(RecipeRepositoryLive),
	Layer.provide(recipeApiRepoLive),
);

const makeSellerUsecaseLive = sellerUsecaseLive.pipe(
	Layer.provide(SellerRepositoryLive),
	Layer.provide(sellerApiRepoLive),
);

const makeWarehouseUsecaseLive = warehouseUsecaseLive.pipe(
	Layer.provide(WarehouseRepositoryLive),
	Layer.provide(warehouseApiRepoLive),
);

const makeChannelUsecaseLive = channelUsecaseLive.pipe(
	Layer.provide(ChannelRepositoryLive),
	Layer.provide(channelApiRepoLive),
);

const MainLayer = Layer.mergeAll(
	makeAreaUsecaseLive,
	makeAuthUsecaseLive,
	makeBrandUsecaseLive,
	makeBusinessLineUsecaseLive,
	makeCategoryUsecaseLive,
	makeMeasurementUnitUsecaseLive,
	makeUnitMeasurementCategoryUsecaseLive,
	makeOperationUsecaseLive,
	makeProductUsecaseLive,
	makeRecipeUsecaseLive,
	makeSellerUsecaseLive,
	makeWorkAreaUsecaseLive,
	makeProductionFlowUsecaseLive,
	makeWarehouseUsecaseLive,
	makeChannelUsecaseLive,
);

export const AppRuntime = ManagedRuntime.make(MainLayer);
