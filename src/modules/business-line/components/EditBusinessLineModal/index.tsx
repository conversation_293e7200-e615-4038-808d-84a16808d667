import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useService } from "src/config/context/serviceProvider";
import TextModal from "src/core/components/TextModal";
import { getErrorResult } from "src/core/utils/effectErrors";
import { businessLineOptionsById } from "../../hooks/business-line-options";
import EditBusinessLineForm from "./EditBusinessLineForm";

interface Props {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	id: string;
	parentId?: string;
}

export default function EditBusinessLineModal({
	isOpen,
	setIsOpen,
	id,
	parentId,
}: Props) {
	const svc = useService();

	const { data, isError, error, isPending } = useQuery({
		...businessLineOptionsById(svc, id),
		enabled: isOpen,
	});

	useEffect(() => {
		if (error) {
			console.log(getErrorResult(error).error);
		}
	}, [error]);

	if (isError) {
		return (
			<TextModal
				open={isOpen}
				title="Error"
				text={getErrorResult(error).error.message}
			/>
		);
	}

	if (isPending) {
		return (
			<TextModal
				open={isOpen}
				title="Cargando..."
				text="Cargando datos de la línea de negocio..."
			/>
		);
	}

	return (
		<div className={`modal ${isOpen ? "modal-open" : ""}`}>
			<div className="modal-box">
				<h3 className="font-bold text-lg">Editar Línea de Negocio</h3>
				<EditBusinessLineForm
					businessLine={data}
					setIsOpen={setIsOpen}
					parentId={parentId}
				/>
			</div>
		</div>
	);
}
