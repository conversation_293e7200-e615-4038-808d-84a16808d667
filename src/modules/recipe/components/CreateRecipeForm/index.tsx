import { useQuery } from "@tanstack/react-query";
import { Link, useNavigate } from "@tanstack/react-router";
import { Hash, Plus, Tag, Trash2 } from "lucide-react";
import { toast } from "react-toastify";
import { useService } from "src/config/context/serviceProvider";
import { useAppForm } from "src/core/components/form/form";
import { AppRuntime } from "src/core/service/utils/runtimes";
import { getErrorResult } from "src/core/utils/effectErrors";
import { CategoryCode } from "~/modules/category/service/model/category";
import { measurementUnitOptions } from "~/modules/measurement-unit/hooks/measurement-unit-options";
import { productOptionsByCategoryCode } from "~/modules/product/hooks/product-options";
import useCreateRecipe from "../../hooks/use-create-recipe";
import { CreateRecipeSchema } from "../schema";

interface Props {
	recipeType: "bulk" | "unit";
}

export default function CreateRecipeForm({ recipeType }: Props) {
	const service = useService();
	const { recipe } = service;
	const { mutate, isPending } = useCreateRecipe();
	const navigate = useNavigate();
	const { data: resultantProducts = [] } = useQuery(
		productOptionsByCategoryCode(service, CategoryCode.PRODUCTS),
	);
	const { data: materials = [] } = useQuery(
		productOptionsByCategoryCode(service, CategoryCode.MATERIALS),
	);
	const { data: rawMaterials = [] } = useQuery(
		productOptionsByCategoryCode(service, CategoryCode.RAW_MATERIALS),
	);
	const { data: suppliers = [] } = useQuery(
		productOptionsByCategoryCode(service, CategoryCode.SUPPLIERS),
	);
	const { data: measurementUnits = [] } = useQuery(
		measurementUnitOptions(service),
	);

	const componentProducts = [...materials, ...rawMaterials, ...suppliers];

	const form = useAppForm({
		defaultValues: {
			name: "",
			code: "",
			type: recipeType,
			batchSize: 1,
			measurementUnitID: "",
			productIDs: [],
			components: [],
		} as CreateRecipeSchema,
		validators: {
			onChange: CreateRecipeSchema,
		},
		onSubmit: ({ value }) => {
			mutate(value, {
				onError: (_error) => {
					console.log(_error);
					const { error } = getErrorResult(_error);
					toast.error(error.message);
				},
				onSettled: () => {
					navigate({ to: "/admin/manufacture/recipes" });
					toast.success("Receta creada exitosamente");
				},
			});
		},
	});

	function getMeasurementUnitName(materialId: string) {
		const measurementId = materials.find(
			(mat) => mat.id === materialId,
		)?.measurementUnitID;

		return measurementId
			? measurementUnits.find((unit) => unit.id === measurementId)?.name
			: null;
	}

	return (
		<form
			onSubmit={(e) => {
				e.preventDefault();
				e.stopPropagation();
				form.handleSubmit();
			}}
		>
			<form.AppForm>
				<fieldset className="fieldset">
					<div className="mb-4">
						<h3 className="mb-2 font-semibold text-lg">Información Básica</h3>
						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							<form.AppField
								name="name"
								validators={{
									onChangeAsyncDebounceMs: 500,
									onChangeAsync: async ({ value }) => {
										if (!value || value.trim() === "") {
											return undefined;
										}
										try {
											await AppRuntime.runPromise(recipe.validateName(value));
											return undefined;
										} catch (e) {
											return [{ message: "El nombre ya existe" }];
										}
									},
								}}
								children={({ FSTextField }) => (
									<FSTextField
										label="Nombre"
										placeholder="Nombre de la receta"
										prefixComponent={<Tag size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="code"
								validators={{
									onChangeAsyncDebounceMs: 500,
									onChangeAsync: async ({ value }) => {
										if (!value || value.trim() === "") {
											return undefined;
										}
										try {
											await AppRuntime.runPromise(recipe.validateCode(value));
											return undefined;
										} catch (e) {
											return [{ message: "El código ya existe" }];
										}
									},
								}}
								children={({ FSTextField }) => (
									<FSTextField
										label="Código"
										placeholder="Código de la receta"
										prefixComponent={<Hash size={16} />}
									/>
								)}
							/>
						</div>
						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							<form.AppField
								name="batchSize"
								children={({ FSTextField }) => (
									<FSTextField
										label="Tamaño de Lote"
										placeholder="1"
										type="number"
									/>
								)}
							/>
							<form.AppField
								name="measurementUnitID"
								children={({ FSSelectField }) => (
									<FSSelectField
										label="Unidad de Medida"
										placeholder="Seleccionar unidad"
										options={measurementUnits.map((unit) => ({
											value: unit.id,
											label: unit.name,
										}))}
									/>
								)}
							/>
						</div>
					</div>

					<div className="mb-4">
						<h3 className="mb-2 font-semibold text-lg">
							Productos Resultantes
						</h3>
						<form.AppField
							name="productIDs"
							children={({ FSComboBoxField }) => (
								<FSComboBoxField
									label="Productos"
									placeholder="Seleccionar productos que resultan de esta receta"
									options={resultantProducts.map((product) => ({
										value: product.id,
										label: `${product.name} (${product.code})`,
									}))}
									isMultiple={true}
								/>
							)}
						/>
					</div>

					<div className="mb-4">
						<div className="mb-4 flex items-center justify-between">
							<h3 className="font-semibold text-lg">
								Componentes de la Receta
							</h3>
							<form.AppField
								name="components"
								mode="array"
								children={(field) => (
									<button
										type="button"
										className="btn btn-primary btn-sm"
										onClick={() =>
											field.pushValue({
												productID: "",
												quantity: 1,
											})
										}
									>
										<Plus size={16} />
										Agregar Componente
									</button>
								)}
							/>
						</div>

						<form.AppField
							name="components"
							mode="array"
							children={(field) => (
								<div>
									{field.state.value.length === 0 ? (
										<div className="py-8 text-center text-gray-500">
											No hay componentes agregados. Haz clic en "Agregar
											Componente" para comenzar.
										</div>
									) : (
										<table className="table-zebra table w-full">
											<thead>
												<tr>
													<th>Producto</th>
													<th>Cantidad</th>
													<th>Acciones</th>
												</tr>
											</thead>
											<tbody>
												{field.state.value.map(({ productID }, i) => (
													<tr key={productID}>
														<td>
															<form.AppField
																name={`components[${i}].productID`}
																children={({ FSComboBoxField }) => (
																	<FSComboBoxField
																		placeholder="Seleccionar producto"
																		options={componentProducts.map(
																			(product) => ({
																				value: product.id,
																				label: `${product.name} (${product.code})`,
																			}),
																		)}
																	/>
																)}
															/>
														</td>
														<td className="flex items-center gap-2">
															<form.AppField
																name={`components[${i}].quantity`}
																children={({ FSTextField }) => (
																	<FSTextField placeholder="0" type="number" />
																)}
															/>
															<form.Subscribe
																selector={(state) =>
																	state.values.components[i]?.productID
																}
																children={(productId) =>
																	productId
																		? getMeasurementUnitName(productId)
																		: null
																}
															/>
														</td>
														<td>
															<button
																type="button"
																className="btn btn-error btn-sm"
																onClick={() => field.removeValue(i)}
															>
																<Trash2 size={16} />
															</button>
														</td>
													</tr>
												))}
											</tbody>
										</table>
									)}
								</div>
							)}
						/>
					</div>
				</fieldset>
				<div className="flex justify-end gap-4">
					<Link to="/admin/manufacture/recipes" className="btn btn-ghost">
						Cancelar
					</Link>
					<form.SubscribeButton
						label="Crear Receta"
						className="btn btn-primary"
						isDisabled={isPending}
					/>
				</div>
			</form.AppForm>
		</form>
	);
}
