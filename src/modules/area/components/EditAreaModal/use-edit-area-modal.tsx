import { toast } from "react-toastify";
import { useAppForm } from "src/core/components/form/form";
import { getErrorResult } from "src/core/utils/effectErrors";
import useUpdateArea from "../../hooks/use-update-area";
import type { Area } from "../../service/model/area";
import { CreateAreaSchema } from "../schema";

export interface EditAreaModalProps {
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	area: Area;
}

export default function useEditAreaModal({
	setIsOpen,
	area,
}: EditAreaModalProps) {
	const { mutate } = useUpdateArea();

	const form = useAppForm({
		defaultValues: {
			name: area.name,
			deliveryDays: area.deliveryDays,
			state: area.state,
		} as CreateAreaSchema,
		validators: {
			onChange: CreateAreaSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					id: area.id,
					name: value.name,
					deliveryDays: value.deliveryDays,
					state: value.state,
				},
				{
					onSuccess: () => {
						toast.success("Área actualizada");
						handleClose();
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
	};
}
