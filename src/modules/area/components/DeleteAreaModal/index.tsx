import { toast } from "react-toastify";
import { cn } from "src/core/utils/classes";
import { getErrorResult } from "src/core/utils/effectErrors";
import useDeleteArea from "../../hooks/use-delete-area";
import type { Area } from "../../service/model/area";

interface Props {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	area: Area;
}

export default function DeleteAreaModal({ isOpen, setIsOpen, area }: Props) {
	const { mutate, isPending } = useDeleteArea();

	const handleDelete = () => {
		mutate(area.id, {
			onSuccess: () => {
				toast.success("Área eliminada");
				setIsOpen(false);
			},
			onError: (_error) => {
				console.log(_error);
				const { error } = getErrorResult(_error);
				toast.error(error.message);
			},
		});
	};

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<h3 className="font-bold text-lg">Eliminar Área</h3>
				<p className="py-4">
					¿Estás seguro de que deseas eliminar el área "{area.name}"? Esta
					acción no se puede deshacer.
				</p>
				<div className="modal-action">
					<button
						type="button"
						className="btn btn-ghost"
						onClick={() => setIsOpen(false)}
						disabled={isPending}
					>
						Cancelar
					</button>
					<button
						type="button"
						className="btn btn-error"
						onClick={handleDelete}
						disabled={isPending}
					>
						{isPending ? "Eliminando..." : "Eliminar"}
					</button>
				</div>
			</div>
		</div>
	);
}
