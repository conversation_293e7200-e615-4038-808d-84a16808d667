import { Calendar, Hash, Tag } from "lucide-react";
import CloseModal from "src/core/components/CloseModal";
import { cn } from "src/core/utils/classes";
import type { CreateAreaModalProps } from "./use-create-modal";
import useCreateAreaModal from "./use-create-modal";

const dayOptions = [
	{ value: 0, label: "Domingo" },
	{ value: 1, label: "Lunes" },
	{ value: 2, label: "Martes" },
	{ value: 3, label: "Miércoles" },
	{ value: 4, label: "Jueves" },
	{ value: 5, label: "Viernes" },
	{ value: 6, label: "Sábado" },
];

export default function CreateAreaModal({
	isOpen,
	setIsOpen,
}: CreateAreaModalProps) {
	const { form, handleClose, isPending } = useCreateAreaModal({
		isOpen,
		setIsOpen,
	});

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={handleClose} />
				<h3 className="font-bold text-lg"><PERSON><PERSON><PERSON></h3>
				<form
					onSubmit={(e) => {
						e.preventDefault();
						form.handleSubmit();
					}}
				>
					<form.AppForm>
						<fieldset className="fieldset">
							<form.AppField
								name="name"
								children={({ FSTextField }) => (
									<FSTextField
										label="Nombre"
										placeholder="Nombre del área"
										prefixComponent={<Tag size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="deliveryDays"
								children={({ FSCheckboxGroupField }) => (
									<FSCheckboxGroupField
										label="Días de Entrega"
										options={dayOptions}
									/>
								)}
							/>
							<form.AppField
								name="state"
								children={({ FSSelectField }) => (
									<FSSelectField
										label="Estado"
										placeholder="Seleccionar estado"
										prefixComponent={<Hash size={16} />}
										options={[
											{ value: "active", label: "Activo" },
											{ value: "inactive", label: "Inactivo" },
										]}
									/>
								)}
							/>
						</fieldset>
						<div className="modal-action">
							<form.SubscribeButton
								label="Crear"
								className="btn btn-primary"
								isDisabled={isPending}
							/>
						</div>
					</form.AppForm>
				</form>
			</div>
		</div>
	);
}
