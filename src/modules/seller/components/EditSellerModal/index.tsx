import { useQuery } from "@tanstack/react-query";
import { User, Users, IdCard, FileText } from "lucide-react";
import { useService } from "src/config/context/serviceProvider";
import CloseModal from "src/core/components/CloseModal";
import { cn } from "src/core/utils/classes";
import { getErrorResult } from "src/core/utils/effectErrors";
import { sellerOptionsById } from "../../hooks/seller-options";
import useEditSellerModal from "./use-edit-seller-modal";

export interface EditSellerModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	id: string;
}

export default function EditSellerModal({
	isOpen,
	setIsOpen,
	id,
}: EditSellerModalProps) {
	const svc = useService();
	const { data: seller, isError, error, isPending } = useQuery({
		...sellerOptionsById(svc, id),
		enabled: isOpen,
	});

	const { form, handleClose } = useEditSellerModal({
		isOpen,
		setIsOpen,
		seller: seller!,
	});

	if (!isOpen) return null;

	if (isPending) {
		return (
			<div className={cn("modal", isOpen && "modal-open")}>
				<div className="modal-box">
					<div>Cargando...</div>
				</div>
			</div>
		);
	}

	if (isError) {
		return (
			<div className={cn("modal", isOpen && "modal-open")}>
				<div className="modal-box">
					<div>Error: {getErrorResult(error).error.message}</div>
				</div>
			</div>
		);
	}

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={handleClose} />
				<h3 className="font-bold text-lg">Editar Vendedor</h3>
				<form
					onSubmit={(e) => {
						e.preventDefault();
						form.handleSubmit();
					}}
				>
					<form.AppForm>
						<fieldset className="fieldset">
							<form.AppField
								name="name"
								children={({ FSTextField }) => (
									<FSTextField
										label="Nombre"
										placeholder="Nombre del vendedor"
										prefixComponent={<User size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="fatherName"
								children={({ FSTextField }) => (
									<FSTextField
										label="Apellido Paterno"
										placeholder="Apellido paterno"
										prefixComponent={<Users size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="motherName"
								children={({ FSTextField }) => (
									<FSTextField
										label="Apellido Materno"
										placeholder="Apellido materno"
										prefixComponent={<Users size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="identityDocumentNumber"
								children={({ FSTextField }) => (
									<FSTextField
										label="Documento de Identidad"
										placeholder="Número de documento"
										prefixComponent={<IdCard size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="state"
								children={({ FSSelectField }) => (
									<FSSelectField
										label="Estado"
										placeholder="Seleccionar estado"
										prefixComponent={<FileText size={16} />}
										options={[
											{ value: "ACTIVE", label: "Activo" },
											{ value: "INACTIVE", label: "Inactivo" },
										]}
									/>
								)}
							/>
						</fieldset>
						<div className="modal-action">
							<form.SubscribeButton
								label="Actualizar"
								className="btn btn-primary"
							/>
						</div>
					</form.AppForm>
				</form>
			</div>
		</div>
	);
}
